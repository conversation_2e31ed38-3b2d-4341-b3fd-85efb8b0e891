/**
 * 专家技术规格导出工具
 * 用于将优化建议的技术参数导出为不同格式，便于发送给Augment进行系统升级
 */

export interface OptimizationAction {
  action_id?: string;
  action_type: string;
  description: string;
  impact_score: number;
  priority: number;
  auto_executable: boolean;
  ai_technical_spec?: {
    optimization_category: string;
    code_changes_required: string[];
    execution_strategy: string;
    success_criteria: string[];
    monitoring_metrics: string[];
    claude_prompts: string[];
    execution_commands: string[];
    estimated_time: string;
    risk_assessment: string;
  };
  period_validation?: {
    primary_period: string;
    validated_count: number;
    total_count: number;
    validation_rate: number;
    summary: string;
  };
}

export interface ExportData {
  export_info: {
    period: string;
    total_actions: number;
    validation_rate: number;
    export_time: string;
    data_source: string;
  };
  optimization_actions: OptimizationAction[];
}

/**
 * 格式化单个优化建议为Markdown格式
 */
export const formatSingleActionToMarkdown = (action: OptimizationAction, index: number): string => {
  const spec = action.ai_technical_spec;
  if (!spec) {
    return `### ${index}. ${action.description}\n**⚠️ 技术规格数据不完整**\n\n`;
  }

  // 🔧 安全处理数组字段，防止undefined错误
  const safeArray = (arr: any[] | undefined): any[] => Array.isArray(arr) ? arr : [];
  const safeString = (str: string | undefined): string => str || '未定义';

  return `### ${index}. ${action.description}

#### 🔧 技术参数
- **优化类别**: ${safeString(spec.optimization_category)}
- **代码修改要求**: ${safeArray(spec.code_changes_required).join('、') || '未定义'}
- **风险评估**: ${safeString(spec.risk_assessment)}
- **预计用时**: ${safeString(spec.estimated_time)}

#### ⚡ 执行策略
\`\`\`
${safeString(spec.execution_strategy)}
\`\`\`

#### ✅ 成功标准与监控
${safeArray(spec.success_criteria).map(criteria => `- ${criteria}`).join('\n') || '- 未定义成功标准'}

**监控指标**:
${safeArray(spec.monitoring_metrics).map(metric => `- ${metric}`).join('\n') || '- 未定义监控指标'}

#### 🤖 Claude 4 提示
\`\`\`
${safeArray(spec.claude_prompts).join('\n') || '未定义Claude提示'}
\`\`\`

#### 💻 执行命令
\`\`\`bash
${safeArray(spec.execution_commands).join('\n') || '未定义执行命令'}
\`\`\`

---

`;
};

/**
 * 格式化完整技术规格为Markdown格式（适合发给Augment）
 */
export const formatTechnicalSpecToMarkdown = (data: ExportData): string => {
  const { export_info, optimization_actions } = data;
  
  const header = `# 🎯 福彩3D系统优化技术规格

## 📊 基础信息
- **期号**: ${export_info.period}期
- **优化建议数量**: ${export_info.total_actions}个
- **数据验证率**: ${export_info.validation_rate.toFixed(1)}%
- **数据源**: ${export_info.data_source}
- **导出时间**: ${export_info.export_time}

## 🚀 系统升级建议

基于用户反馈分析，系统识别出以下优化机会。这些技术规格可直接用于Augment系统升级和主动优化迭代。

## 📋 优化建议详情

`;

  const actionsMarkdown = optimization_actions
    .map((action, index) => formatSingleActionToMarkdown(action, index + 1))
    .join('');

  // 🔧 安全处理优化建议数组
  const safeOptimizationActions = Array.isArray(optimization_actions) ? optimization_actions : [];

  const footer = `## 🎯 实施建议

### 优先级排序
${safeOptimizationActions.length > 0 ?
  safeOptimizationActions
    .sort((a, b) => (b.priority || 0) - (a.priority || 0))
    .slice(0, 3)
    .map((action, index) => `${index + 1}. ${action.description || '未知优化建议'} (优先级: ${action.priority || 0})`)
    .join('\n') : '暂无优化建议'}

### 自动执行建议
${safeOptimizationActions.length > 0 ?
  safeOptimizationActions
    .filter(action => action.auto_executable)
    .map(action => `- ${action.description || '未知优化建议'}`)
    .join('\n') || '- 暂无自动执行建议' : '- 暂无自动执行建议'}

### 风险控制
- 建议分批执行，优先处理低风险项目
- 每次执行后进行充分测试验证
- 保持完整的回滚机制

---
*本技术规格由福彩3D智能预测系统自动生成，基于真实用户反馈分析*
`;

  return header + actionsMarkdown + footer;
};

/**
 * 格式化技术规格为JSON格式（适合程序处理）
 */
export const formatTechnicalSpecToJSON = (data: ExportData): string => {
  // 🔧 安全处理数组字段，防止undefined错误
  const safeArray = (arr: any[] | undefined): any[] => Array.isArray(arr) ? arr : [];
  const safeOptimizationActions = Array.isArray(data.optimization_actions) ? data.optimization_actions : [];

  const jsonData = {
    export_metadata: {
      format_version: "1.0",
      generator: "福彩3D智能预测系统",
      export_purpose: "Augment系统升级",
      ...data.export_info
    },
    technical_specifications: safeOptimizationActions.map(action => ({
      action_id: action.action_id || `action_${Date.now()}`,
      action_type: action.action_type || 'unknown',
      description: action.description || '未知优化建议',
      priority: action.priority || 0,
      impact_score: action.impact_score || 0,
      auto_executable: action.auto_executable || false,
      technical_details: action.ai_technical_spec ? {
        optimization_category: action.ai_technical_spec.optimization_category || '未知类别',
        code_changes_required: safeArray(action.ai_technical_spec.code_changes_required),
        execution_strategy: action.ai_technical_spec.execution_strategy || '未定义',
        success_criteria: safeArray(action.ai_technical_spec.success_criteria),
        monitoring_metrics: safeArray(action.ai_technical_spec.monitoring_metrics),
        claude_prompts: safeArray(action.ai_technical_spec.claude_prompts),
        execution_commands: safeArray(action.ai_technical_spec.execution_commands),
        estimated_time: action.ai_technical_spec.estimated_time || '未知',
        risk_assessment: action.ai_technical_spec.risk_assessment || '未评估'
      } : null,
      period_validation: action.period_validation || null
    })),
    implementation_recommendations: {
      priority_order: safeOptimizationActions
        .sort((a, b) => (b.priority || 0) - (a.priority || 0))
        .slice(0, 3)
        .map(action => ({
          description: action.description || '未知优化建议',
          priority: action.priority || 0,
          auto_executable: action.auto_executable || false
        })),
      auto_executable_count: safeOptimizationActions.filter(action => action.auto_executable).length,
      manual_review_count: safeOptimizationActions.filter(action => !action.auto_executable).length,
      risk_distribution: {
        low: safeOptimizationActions.filter(action =>
          action.ai_technical_spec?.risk_assessment?.includes('低风险')).length,
        medium: safeOptimizationActions.filter(action =>
          action.ai_technical_spec?.risk_assessment?.includes('中等风险')).length,
        high: safeOptimizationActions.filter(action =>
          action.ai_technical_spec?.risk_assessment?.includes('高风险')).length
      }
    }
  };

  return JSON.stringify(jsonData, null, 2);
};

/**
 * 复制文本到剪贴板
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案：使用传统方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    return false;
  }
};

/**
 * 下载文件
 */
export const downloadFile = (content: string, filename: string, contentType: string = 'text/plain'): void => {
  try {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('文件下载失败:', error);
    throw new Error('文件下载失败');
  }
};

/**
 * 生成导出文件名
 */
export const generateExportFilename = (period: string, format: 'md' | 'json', timestamp?: string): string => {
  const time = timestamp || new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  return `fucai3d_optimization_specs_${period}_${time}.${format}`;
};

/**
 * 准备导出数据
 */
export const prepareExportData = (actions: OptimizationAction[]): ExportData => {
  // 提取期号信息
  const primaryPeriod = actions[0]?.period_validation?.primary_period || '未知期号';
  const validationRate = actions[0]?.period_validation?.validation_rate || 0;
  const totalActions = actions.length;

  return {
    export_info: {
      period: primaryPeriod,
      total_actions: totalActions,
      validation_rate: validationRate,
      export_time: new Date().toLocaleString('zh-CN'),
      data_source: `基于${primaryPeriod}期用户反馈分析`
    },
    optimization_actions: actions
  };
};
