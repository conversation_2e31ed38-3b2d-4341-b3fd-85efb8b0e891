import React, { useState } from 'react';
import { <PERSON>, Collapse, Tag, Space, Typo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>vide<PERSON>, <PERSON><PERSON>, message } from 'antd';
import {
  QuestionCircleOutlined,
  BulbOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  CodeOutlined,
  EyeOutlined,
  UserOutlined,
  RobotOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  CopyOutlined,

  FileMarkdownOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import {
  formatTechnicalSpecToMarkdown,
  formatTechnicalSpecToJSON,
  copyToClipboard,
  downloadFile,
  generateExportFilename,
  prepareExportData,
  type OptimizationAction as ExportOptimizationAction
} from '../utils/exportTemplates';

const { Text, Title, Paragraph } = Typography;
const { Panel } = Collapse;

interface UserFriendlyExplanation {
  problem_description: string;
  optimization_reason: string;
  method_explanation: string;
  expected_outcome: string;
  user_action_required: string;
  estimated_improvement: string;
  risk_assessment: string;
  completion_time: string;
}

interface AITechnicalSpec {
  optimization_category: string;
  target_components: string[];
  technical_parameters: any;
  execution_instructions: any;
  rollback_strategy: any;
  success_criteria: string[];
  monitoring_metrics: string[];
  claude_prompt: string;
  execution_commands: string[];
}

interface OptimizationAction {
  action_id: string;
  action_type: string;
  target: string;
  description: string;
  priority: number;
  impact_score: number;
  auto_executable: boolean;
  user_friendly_explanation?: UserFriendlyExplanation;
  ai_technical_spec?: AITechnicalSpec;
  explanation_generated?: boolean;
  // 🆕 期号验证字段
  primary_source_period?: string;
  source_period_count?: number;
  validated_feedback_count?: number;
  total_feedback_count?: number;
  validation_rate?: number;
  feedback_source_summary?: string;
  source_periods?: string[];
}

interface OptimizationExplanationProps {
  action: OptimizationAction;
  viewMode?: 'user' | 'expert' | 'both';
  onViewModeChange?: (mode: 'user' | 'expert' | 'both') => void;
}

const OptimizationExplanation: React.FC<OptimizationExplanationProps> = ({
  action,
  viewMode = 'user',
  onViewModeChange
}) => {
  const [activeKey, setActiveKey] = useState<string[]>(['user-explanation']);
  // 🆕 导出状态管理
  const [exportLoading, setExportLoading] = useState({
    copy: false,
    markdown: false,
    json: false
  });

  const getPriorityColor = (priority: number) => {
    if (priority >= 4) return 'red';
    if (priority >= 3) return 'orange';
    return 'blue';
  };

  const getRiskColor = (risk: string) => {
    if (risk.includes('低风险')) return 'green';
    if (risk.includes('中等风险')) return 'orange';
    return 'red';
  };

  // 🆕 导出功能实现（增强版）
  const handleCopyTechnicalSpec = async () => {
    setExportLoading(prev => ({ ...prev, copy: true }));

    try {
      // 显示进度提示
      const hideLoading = message.loading('正在准备技术规格数据...', 0);

      const exportData = prepareExportData([action as ExportOptimizationAction]);
      const markdownContent = formatTechnicalSpecToMarkdown(exportData);

      // 模拟一点处理时间，让用户看到进度
      await new Promise(resolve => setTimeout(resolve, 500));

      const success = await copyToClipboard(markdownContent);

      hideLoading();

      if (success) {
        message.success({
          content: '✅ 技术规格已复制到剪贴板，可直接发送给Augment！',
          duration: 3,
          style: { marginTop: '20vh' }
        });
      } else {
        message.error({
          content: '❌ 复制失败，请稍后重试',
          duration: 3
        });
      }
    } catch (error) {
      console.error('复制技术规格失败:', error);
      message.error({
        content: '❌ 复制失败，请稍后重试',
        duration: 3
      });
    } finally {
      setExportLoading(prev => ({ ...prev, copy: false }));
    }
  };

  const handleDownloadMarkdown = async () => {
    setExportLoading(prev => ({ ...prev, markdown: true }));

    try {
      // 显示进度提示
      const hideLoading = message.loading('正在生成Markdown文件...', 0);

      const exportData = prepareExportData([action as ExportOptimizationAction]);
      const markdownContent = formatTechnicalSpecToMarkdown(exportData);
      const period = action.period_validation?.primary_period || '未知期号';
      const filename = generateExportFilename(period, 'md');

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 800));

      downloadFile(markdownContent, filename, 'text/markdown');

      hideLoading();

      message.success({
        content: `📄 Markdown文件已下载: ${filename}`,
        duration: 4,
        style: { marginTop: '20vh' }
      });
    } catch (error) {
      console.error('下载Markdown失败:', error);
      message.error({
        content: '❌ 下载失败，请稍后重试',
        duration: 3
      });
    } finally {
      setExportLoading(prev => ({ ...prev, markdown: false }));
    }
  };

  const handleDownloadJSON = async () => {
    setExportLoading(prev => ({ ...prev, json: true }));

    try {
      // 显示进度提示
      const hideLoading = message.loading('正在生成JSON文件...', 0);

      const exportData = prepareExportData([action as ExportOptimizationAction]);
      const jsonContent = formatTechnicalSpecToJSON(exportData);
      const period = action.period_validation?.primary_period || '未知期号';
      const filename = generateExportFilename(period, 'json');

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 600));

      downloadFile(jsonContent, filename, 'application/json');

      hideLoading();

      message.success({
        content: `📊 JSON文件已下载: ${filename}`,
        duration: 4,
        style: { marginTop: '20vh' }
      });
    } catch (error) {
      console.error('下载JSON失败:', error);
      message.error({
        content: '❌ 下载失败，请稍后重试',
        duration: 3
      });
    } finally {
      setExportLoading(prev => ({ ...prev, json: false }));
    }
  };

  // 🆕 渲染导出按钮组（增强版）
  const renderExportButtons = () => {
    if (viewMode !== 'expert' && viewMode !== 'both') return null;
    if (!action.ai_technical_spec) return null;

    return (
      <Space style={{ marginBottom: 16 }}>
        <Text strong>📤 导出技术规格：</Text>
        <Tooltip title="复制Markdown格式到剪贴板，适合发送给Augment">
          <Button
            type="primary"
            icon={<CopyOutlined />}
            onClick={handleCopyTechnicalSpec}
            loading={exportLoading.copy}
            disabled={exportLoading.markdown || exportLoading.json}
            size="small"
          >
            {exportLoading.copy ? '复制中...' : '复制规格'}
          </Button>
        </Tooltip>
        <Tooltip title="下载Markdown文件">
          <Button
            icon={<FileMarkdownOutlined />}
            onClick={handleDownloadMarkdown}
            loading={exportLoading.markdown}
            disabled={exportLoading.copy || exportLoading.json}
            size="small"
          >
            {exportLoading.markdown ? '生成中...' : '下载MD'}
          </Button>
        </Tooltip>
        <Tooltip title="下载JSON文件">
          <Button
            icon={<FileTextOutlined />}
            onClick={handleDownloadJSON}
            loading={exportLoading.json}
            disabled={exportLoading.copy || exportLoading.markdown}
            size="small"
          >
            {exportLoading.json ? '生成中...' : '下载JSON'}
          </Button>
        </Tooltip>
        {(exportLoading.copy || exportLoading.markdown || exportLoading.json) && (
          <Text type="secondary" style={{ fontSize: '12px' }}>
            正在处理，请稍候...
          </Text>
        )}
      </Space>
    );
  };

  const renderUserFriendlyView = () => {
    if (!action.user_friendly_explanation) {
      return (
        <Alert
          message="说明生成中"
          description="正在为您生成易懂的优化说明，请稍候..."
          type="info"
          showIcon
        />
      );
    }

    const explanation = action.user_friendly_explanation;

    return (
      <Card 
        title={
          <Space>
            <UserOutlined style={{ color: '#1890ff' }} />
            <span>用户友好说明</span>
            <Tag color={getPriorityColor(action.priority)}>
              优先级 {action.priority}
            </Tag>
            {action.auto_executable && (
              <Tag color="green">可自动执行</Tag>
            )}
          </Space>
        }
        size="small"
      >
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          {/* 🆕 期号信息显示 */}
          {(action.primary_source_period || action.validation_rate !== undefined) && (
            <div style={{ marginBottom: 8 }}>
              <Space wrap>
                {action.primary_source_period && (
                  <Tag icon={<CalendarOutlined />} color="blue">
                    基于{action.primary_source_period}期反馈
                  </Tag>
                )}
                {action.validation_rate !== undefined && action.validation_rate > 0 && (
                  <Tag icon={<CheckCircleOutlined />} color="green">
                    验证通过率: {(action.validation_rate * 100).toFixed(1)}%
                  </Tag>
                )}
                {action.source_period_count && action.source_period_count > 1 && (
                  <Tag icon={<InfoCircleOutlined />} color="cyan">
                    涉及{action.source_period_count}个期号
                  </Tag>
                )}
                {action.validated_feedback_count && action.total_feedback_count && (
                  <Tag color="purple">
                    有效反馈: {action.validated_feedback_count}/{action.total_feedback_count}
                  </Tag>
                )}
              </Space>
            </div>
          )}

          {/* 问题描述 */}
          <div>
            <Space>
              <QuestionCircleOutlined style={{ color: '#ff4d4f' }} />
              <Text strong>发现的问题：</Text>
            </Space>
            <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
              {explanation.problem_description}
            </Paragraph>
          </div>

          {/* 优化原因 */}
          <div>
            <Space>
              <BulbOutlined style={{ color: '#faad14' }} />
              <Text strong>为什么要优化：</Text>
            </Space>
            <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
              {explanation.optimization_reason}
            </Paragraph>
          </div>

          {/* 解决方法 */}
          <div>
            <Space>
              <ToolOutlined style={{ color: '#1890ff' }} />
              <Text strong>解决方法：</Text>
            </Space>
            <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
              {explanation.method_explanation}
            </Paragraph>
          </div>

          {/* 预期效果 */}
          <div>
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <Text strong>预期效果：</Text>
            </Space>
            <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
              {explanation.expected_outcome}
            </Paragraph>
            <Tag color="blue">{explanation.estimated_improvement}</Tag>
          </div>

          {/* 用户操作 */}
          <div>
            <Space>
              <EyeOutlined style={{ color: '#722ed1' }} />
              <Text strong>您需要做什么：</Text>
            </Space>
            <Paragraph style={{ marginTop: 8, marginBottom: 0 }}>
              {explanation.user_action_required}
            </Paragraph>
          </div>

          {/* 风险评估和完成时间 */}
          <Space split={<Divider type="vertical" />}>
            <div>
              <Space>
                <ExclamationCircleOutlined style={{ color: getRiskColor(explanation.risk_assessment) }} />
                <Text strong>风险评估：</Text>
              </Space>
              <br />
              <Tag color={getRiskColor(explanation.risk_assessment)}>
                {explanation.risk_assessment}
              </Tag>
            </div>
            <div>
              <Space>
                <ClockCircleOutlined style={{ color: '#1890ff' }} />
                <Text strong>预计用时：</Text>
              </Space>
              <br />
              <Tag color="blue">{explanation.completion_time}</Tag>
            </div>
          </Space>
        </Space>
      </Card>
    );
  };

  const renderTechnicalView = () => {
    if (!action.ai_technical_spec) {
      return (
        <Alert
          message="技术规格生成中"
          description="正在生成AI技术规格，请稍候..."
          type="info"
          showIcon
        />
      );
    }

    const spec = action.ai_technical_spec;

    return (
      <Card 
        title={
          <Space>
            <RobotOutlined style={{ color: '#722ed1' }} />
            <span>AI技术规格</span>
            <Tag color="purple">专家视图</Tag>
          </Space>
        }
        size="small"
      >
        {/* 🆕 期号信息显示（专家视图） */}
        {(action.primary_source_period || action.validation_rate !== undefined) && (
          <div style={{ marginBottom: 12 }}>
            <Space wrap>
              {action.feedback_source_summary && (
                <Tag icon={<CalendarOutlined />} color="blue">
                  {action.feedback_source_summary}
                </Tag>
              )}
              {action.validation_rate !== undefined && (
                <Tag icon={<CheckCircleOutlined />} color="green">
                  数据验证率: {(action.validation_rate * 100).toFixed(1)}%
                </Tag>
              )}
              {action.source_periods && action.source_periods.length > 0 && (
                <Tooltip title={`相关期号: ${action.source_periods.join(', ')}`}>
                  <Tag icon={<InfoCircleOutlined />} color="cyan">
                    数据源: {action.source_periods.length}个期号
                  </Tag>
                </Tooltip>
              )}
            </Space>
          </div>
        )}

        <Collapse
          activeKey={activeKey}
          onChange={setActiveKey}
          size="small"
        >
          <Panel 
            header="技术参数" 
            key="technical-params"
            extra={<CodeOutlined />}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>优化类别：</Text>
                <Tag color="blue">{spec.optimization_category}</Tag>
              </div>
              <div>
                <Text strong>代码修改要求：</Text>
                <Space wrap>
                  {spec.code_changes_required?.map((change, index) => (
                    <Tag key={index} color="green">{change}</Tag>
                  )) || []}
                </Space>
              </div>
              <div>
                <Text strong>技术参数：</Text>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '8px', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  maxHeight: '200px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(spec.technical_parameters, null, 2)}
                </pre>
              </div>
            </Space>
          </Panel>

          <Panel 
            header="执行策略" 
            key="execution-strategy"
            extra={<ToolOutlined />}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>执行指令：</Text>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '8px', 
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(spec.execution_instructions, null, 2)}
                </pre>
              </div>
              <div>
                <Text strong>回滚策略：</Text>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: '8px', 
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  {JSON.stringify(spec.rollback_strategy, null, 2)}
                </pre>
              </div>
            </Space>
          </Panel>

          <Panel 
            header="成功标准与监控" 
            key="success-monitoring"
            extra={<CheckCircleOutlined />}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>测试要求：</Text>
                <ul style={{ marginTop: 8 }}>
                  {spec.testing_requirements?.map((requirement, index) => (
                    <li key={index}>{requirement}</li>
                  )) || []}
                </ul>
              </div>
              <div>
                <Text strong>监控点：</Text>
                <Space wrap>
                  {spec.monitoring_points?.map((point, index) => (
                    <Tag key={index} color="orange">{point}</Tag>
                  )) || []}
                </Space>
              </div>
            </Space>
          </Panel>

          <Panel 
            header="Claude 4 提示" 
            key="claude-prompt"
            extra={<RobotOutlined />}
          >
            <pre style={{ 
              background: '#f0f2f5', 
              padding: '12px', 
              borderRadius: '4px',
              fontSize: '12px',
              whiteSpace: 'pre-wrap',
              maxHeight: '300px',
              overflow: 'auto'
            }}>
              {spec.claude_prompt}
            </pre>
          </Panel>

          <Panel 
            header="执行命令" 
            key="execution-commands"
            extra={<CodeOutlined />}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {spec.execution_commands?.map((command, index) => (
                <Tag key={index} color="geekblue">{command}</Tag>
              )) || []}
            </Space>
          </Panel>
        </Collapse>
      </Card>
    );
  };

  const renderViewModeSelector = () => {
    if (!onViewModeChange) return null;

    return (
      <Space style={{ marginBottom: 16 }}>
        <Text strong>视图模式：</Text>
        <Button.Group>
          <Button 
            type={viewMode === 'user' ? 'primary' : 'default'}
            icon={<UserOutlined />}
            onClick={() => onViewModeChange('user')}
          >
            用户友好
          </Button>
          <Button 
            type={viewMode === 'expert' ? 'primary' : 'default'}
            icon={<RobotOutlined />}
            onClick={() => onViewModeChange('expert')}
          >
            专家技术
          </Button>
          <Button 
            type={viewMode === 'both' ? 'primary' : 'default'}
            icon={<EyeOutlined />}
            onClick={() => onViewModeChange('both')}
          >
            双视图
          </Button>
        </Button.Group>
      </Space>
    );
  };

  return (
    <div>
      {renderViewModeSelector()}
      {renderExportButtons()}

      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {(viewMode === 'user' || viewMode === 'both') && renderUserFriendlyView()}
        {(viewMode === 'expert' || viewMode === 'both') && renderTechnicalView()}
      </Space>

      {!action.explanation_generated && (
        <Alert
          message="说明生成状态"
          description="双层说明正在生成中，部分信息可能暂时不可用。"
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );
};

export default OptimizationExplanation;
